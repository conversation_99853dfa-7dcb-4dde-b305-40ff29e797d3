
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/contexts/I18nContext';
import { Link } from 'react-router-dom';
import { Link } from 'react-router-dom';

const CTASection = () => {
  const { t } = useTranslation();

  return (
    <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Logo */}
        <div className="flex items-center justify-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center border border-white/20">
            <span className="text-white font-bold text-lg">OT</span>
          </div>
          <span className="text-white font-bold text-2xl">OfficeTech Guinea</span>
        </div>

        {/* Slogan */}
        <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed">
          {t('home.ctaSlogan') || 'Securing your digital future with innovative technology solutions and uncompromising expertise.'}
        </p>

        {/* CTA Button */}
        <Button
          asChild
          size="lg"
          className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg font-semibold group"
        >
          <Link to="/contact">
            {t('home.contactTitle') || 'Contact Us Today'}
            <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </Link>
        </Button>
      </div>
    </section>
  );
};

export default CTASection;
