
import { Shield, Globe, GraduationCap, Eye, Server, Wrench, ArrowRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const ServiceCards = () => {
  const services = [
    {
      icon: Shield,
      title: 'Cybersecurity Solutions',
      description: 'Comprehensive digital protection with advanced threat detection, incident response, and 24/7 monitoring services.',
      features: ['Threat Detection', '24/7 Monitoring', 'Incident Response'],
      image: '/img/cyber-security-1186529_1920.png',
      accent: true
    },
    {
      icon: Globe,
      title: 'Web Design & Development',
      description: 'Modern, responsive websites and web applications built with cutting-edge technologies and best practices.',
      features: ['Responsive Design', 'Custom Development', 'SEO Optimization'],
      image: '/img/Coding_img.jpg',
      accent: false
    },
    {
      icon: Eye,
      title: 'Electronic Surveillance',
      description: 'Advanced CCTV systems, access control, and comprehensive security monitoring solutions.',
      features: ['CCTV Systems', 'Access Control', 'Remote Monitoring'],
      image: '/img/img_field_tech.jpg',
      accent: true
    },
    {
      icon: Server,
      title: 'Network Infrastructure',
      description: 'Reliable connectivity solutions designed for optimal performance, scalability, and security.',
      features: ['Network Setup', 'Performance Optimization', 'Security Implementation'],
      image: '/img/img_cables1.jpg',
      accent: false
    },
    {
      icon: GraduationCap,
      title: 'IT Training & Support',
      description: 'Professional development programs and ongoing technical support to enhance your team capabilities.',
      features: ['Technical Training', 'Ongoing Support', 'Certification Programs'],
      image: '/img/img_presentation.jpg',
      accent: true
    },
    {
      icon: Wrench,
      title: 'System Integration',
      description: 'Seamless integration of technology systems for improved efficiency and streamlined workflows.',
      features: ['System Integration', 'Workflow Optimization', 'Custom Solutions'],
      image: '/img/img_typing_white.jpg',
      accent: false
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Our <span className="text-gradient">Services</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive technology solutions designed to secure, connect, and optimize your business operations.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <Card 
                key={index} 
                className={`group hover:shadow-xl transition-all duration-500 border-0 bg-white hover:-translate-y-3 animate-scale-in overflow-hidden ${service.accent ? 'hover:border-accent/20' : 'hover:border-primary/20'}`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                {/* Service Image */}
                <div className="relative h-48 overflow-hidden">
                  <img 
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className={`absolute top-4 left-4 w-12 h-12 ${service.accent ? 'bg-accent' : 'bg-primary'} rounded-lg flex items-center justify-center`}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {service.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed text-sm">
                    {service.description}
                  </p>
                  
                  <div className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <div className={`w-2 h-2 ${service.accent ? 'bg-accent' : 'bg-primary'} rounded-full mr-3`}></div>
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <Button 
                    variant="ghost" 
                    className={`${service.accent ? 'text-accent hover:text-accent/80' : 'text-primary hover:text-primary/80'} p-0 group-hover:translate-x-1 transition-transform duration-300`}
                  >
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ServiceCards;
