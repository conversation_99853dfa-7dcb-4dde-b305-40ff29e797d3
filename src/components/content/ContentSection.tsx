import { ReactNode } from "react";
import { useContentBatch } from "@/hooks/useContent";
import { DynamicContent } from "./DynamicContent";
import { Skeleton } from "@/components/ui/skeleton";
import { EditableText, EditableTextarea } from "./InlineEditor";

interface ContentSectionProps {
  identifiers: string[];
  language?: string;
  className?: string;
  layout?: "grid" | "stack" | "flex" | "custom";
  columns?: number;
  gap?: number;
  fallback?: ReactNode;
  children?: (contentMap: Record<string, any>) => ReactNode;
}

export const ContentSection = ({
  identifiers,
  language = "en",
  className = "",
  layout = "stack",
  columns = 1,
  gap = 4,
  fallback,
  children
}: ContentSectionProps) => {
  const { content: contentMap, isLoading } = useContentBatch(identifiers, language);

  if (isLoading) {
    return (
      <div className={className}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <ContentSectionSkeleton layout={layout} columns={columns} gap={gap} count={identifiers.length} />
        </div>
      </div>
    );
  }

  if (!contentMap || Object.keys(contentMap).length === 0) {
    if (fallback) {
      return <div className={className}>{fallback}</div>;
    }
    return null;
  }

  // If children render prop is provided, use it
  if (children) {
    return (
      <div className={className}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {children(contentMap)}
        </div>
      </div>
    );
  }

  // Otherwise, render based on layout
  return (
    <div className={className}>
      <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ${getLayoutClasses(layout, columns, gap)}`}>
        {identifiers.map((identifier) => {
          const content = contentMap[identifier];
          if (!content) return null;

          return (
            <DynamicContent
              key={identifier}
              identifier={identifier}
              language={language}
            />
          );
        })}
      </div>
    </div>
  );
};

// Get CSS classes for different layouts
const getLayoutClasses = (layout: string, columns: number, gap: number): string => {
  const gapClass = `gap-${gap}`;
  
  switch (layout) {
    case "grid":
      return `grid grid-cols-1 md:grid-cols-${Math.min(columns, 3)} lg:grid-cols-${columns} ${gapClass}`;
    case "flex":
      return `flex flex-wrap ${gapClass}`;
    case "stack":
      return `space-y-${gap}`;
    default:
      return "";
  }
};

// Loading skeleton for content sections
const ContentSectionSkeleton = ({ 
  layout, 
  columns, 
  gap, 
  count 
}: { 
  layout: string; 
  columns: number; 
  gap: number; 
  count: number; 
}) => {
  const skeletons = Array.from({ length: count }, (_, i) => (
    <div key={i} className="space-y-2">
      <Skeleton className="h-6 w-3/4" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-5/6" />
      <Skeleton className="h-24 w-full" />
    </div>
  ));

  return (
    <div className={getLayoutClasses(layout, columns, gap)}>
      {skeletons}
    </div>
  );
};

// Predefined content sections for common use cases
export const HeroSection = ({ identifier = "hero", language, className, fallback }: {
  identifier?: string;
  language?: string;
  className?: string;
  fallback?: React.ReactNode;
}) => (
  <DynamicContent
    identifier={identifier}
    language={language}
    className={className}
    fallback={fallback || (
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-600 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-blue-800 rounded-full blur-3xl animate-pulse-slow animation-delay-1000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className="animate-slide-up">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6 animate-fade-in-up">
                <span className="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">Advanced</span>
                <span className="text-gray-900"> &</span>
                <br />
                <span className="text-gray-900">Security Solutions</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed animate-fade-in-up animation-delay-300">
                Empowering businesses across Equatorial Guinea with comprehensive IT services, cutting-edge web design, cybersecurity solutions, and technology infrastructure.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up animation-delay-600">
                <a
                  href="#"
                  className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg rounded-lg font-semibold group transform hover:scale-105 transition-all duration-300"
                >
                  Get Started Today
                  <svg className="ml-2 h-5 w-5 inline group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </a>
                <a
                  href="#"
                  className="inline-block border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg rounded-lg border font-semibold group transform hover:scale-105 transition-all duration-300"
                >
                  <svg className="mr-2 h-5 w-5 inline group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2v-8a2 2 0 012-2z" />
                  </svg>
                  View Our Work
                </a>
              </div>
            </div>

            {/* Hero Image */}
            <div className="relative animate-slide-up animation-delay-600">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl transform hover:rotate-1 transition-transform duration-500">
                <img
                  src="/img/img_presentation.jpg"
                  alt="Professional IT team working on technology solutions"
                  className="w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-blue-600/80 via-blue-600/20 to-transparent"></div>

                {/* Floating badges */}
                <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg animate-float">
                  <div className="text-blue-600 font-semibold text-sm">24/7 Support</div>
                </div>

                <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg animate-float animation-delay-600">
                  <div className="text-blue-600 font-semibold text-sm">Secure Solutions</div>
                </div>

                {/* Center experience badge */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="bg-white/95 backdrop-blur-sm rounded-full w-24 h-24 flex flex-col items-center justify-center shadow-lg animate-pulse-gentle">
                    <div className="text-2xl font-bold text-blue-600 mb-1">15+</div>
                    <div className="text-xs text-gray-600 text-center leading-tight whitespace-pre-line">Years{'\n'}Experience</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    )}
  />
);

export const ServicesGrid = ({ language, className }: {
  language?: string;
  className?: string;
}) => (
  <ContentSection
    identifiers={["service-cybersecurity", "service-network", "service-surveillance", "service-training"]}
    language={language}
    layout="grid"
    columns={2}
    gap={6}
    className={className}
    fallback={
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[
            { title: "Cybersecurity", subtitle: "Solutions", description: "Protecting Your Digital World at Every Stage. Comprehensive cybersecurity solutions including prevention, detection, response, recovery, and continuous improvement." },
            { title: "Network", subtitle: "Infrastructure", description: "International and national connectivity solutions including MPLS, SD-WAN, and VPL. High-speed internet and private networks with 24/7 support." },
            { title: "Electronic", subtitle: "Security", description: "Comprehensive electronic security solutions including IP cameras, alarm systems, access control, and professional installation with 24/7 monitoring." },
            { title: "IT Training &", subtitle: "Support", description: "Professional IT training programs and technical support to empower your team with the latest technology skills." }
          ].map((service, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg p-6 min-h-[200px] flex flex-col">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                  <span className="text-white font-bold text-sm">
                    {index === 0 ? "🛡️" : index === 1 ? "🌐" : index === 2 ? "👁️" : "🎓"}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-bold text-gray-900 leading-tight break-words">
                    <span className="block">{service.title}</span>
                    <span className="block text-blue-600">{service.subtitle}</span>
                  </h3>
                </div>
              </div>
              <p className="text-gray-600 text-sm leading-relaxed flex-1">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    }
  />
);

export const TestimonialsSection = ({ language, className }: {
  language?: string;
  className?: string;
}) => (
  <ContentSection
    identifiers={["testimonial-1", "testimonial-2", "testimonial-3"]}
    language={language}
    layout="grid"
    columns={3}
    gap={6}
    className={className}
  />
);

export const ContactSection = ({ language, className }: {
  language?: string;
  className?: string;
}) => (
  <DynamicContent
    identifier="contact-info"
    language={language}
    className={className}
    fallback={
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-xl font-bold mb-4">Contact Us</h3>
        <p className="text-gray-600">Contact information will be available soon.</p>
      </div>
    }
  />
);

// Page builder component for dynamic page composition
export const DynamicPage = ({ 
  sections, 
  language, 
  className 
}: {
  sections: Array<{
    identifier: string;
    type?: "content" | "section";
    props?: Record<string, any>;
  }>;
  language?: string;
  className?: string;
}) => {
  return (
    <div className={className}>
      {sections.map((section, index) => {
        if (section.type === "section") {
          // Handle predefined sections
          switch (section.identifier) {
            case "hero":
              return <HeroSection key={index} language={language} {...section.props} />;
            case "services":
              return <ServicesGrid key={index} language={language} {...section.props} />;
            case "testimonials":
              return <TestimonialsSection key={index} language={language} {...section.props} />;
            case "contact":
              return <ContactSection key={index} language={language} {...section.props} />;
            default:
              return null;
          }
        } else {
          // Handle individual content items
          return (
            <DynamicContent
              key={index}
              identifier={section.identifier}
              language={language}
              {...section.props}
            />
          );
        }
      })}
    </div>
  );
};

// Hook for building pages dynamically
export const usePageBuilder = (pageIdentifier: string, language?: string) => {
  // This could fetch page configuration from the database
  // For now, we'll return some default configurations
  
  const pageConfigs: Record<string, any> = {
    home: [
      { identifier: "hero", type: "section" },
      { identifier: "services", type: "section", props: { className: "py-16" } },
      { identifier: "about-intro", type: "content" },
      { identifier: "testimonials", type: "section", props: { className: "py-16 bg-gray-50" } },
      { identifier: "contact", type: "section", props: { className: "py-16" } },
    ],
    services: [
      { identifier: "services-hero", type: "content" },
      { identifier: "network-solutions-detail", type: "content" },
      { identifier: "domestic-services-detail", type: "content" },
      { identifier: "training-programs-detail", type: "content" },
    ],
    about: [
      { identifier: "about-hero", type: "content" },
      { identifier: "company-history", type: "content" },
      { identifier: "team-section", type: "content" },
      { identifier: "values-section", type: "content" },
    ],
    contact: [
      { identifier: "contact-hero", type: "content" },
      { identifier: "contact", type: "section" },
      { identifier: "office-locations", type: "content" },
    ],
  };

  return {
    sections: pageConfigs[pageIdentifier] || [],
    language: language || "en",
  };
};
