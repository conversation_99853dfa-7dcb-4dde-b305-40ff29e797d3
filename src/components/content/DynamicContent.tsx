import { ReactNode } from "react";
import { useContent } from "@/hooks/useContent";
import { ContentType } from "@/types/content";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import { EditableText, EditableTextarea } from "./InlineEditor";
import { useContentContext } from "./ContentProvider";

interface DynamicContentProps {
  identifier: string;
  language?: string;
  fallback?: ReactNode;
  className?: string;
  children?: (content: any, contentType: ContentType) => ReactNode;
}

export const DynamicContent = ({ 
  identifier, 
  language = "en", 
  fallback,
  className,
  children 
}: DynamicContentProps) => {
  const { content, isLoading, error } = useContent(identifier, language);

  if (isLoading) {
    return (
      <div className={className}>
        <ContentSkeleton />
      </div>
    );
  }

  // Check if content exists and is in the requested language
  const shouldUseFallback = error || !content || (language && content.language !== language);

  if (shouldUseFallback) {
    if (fallback) {
      return <div className={className}>{fallback}</div>;
    }

    return (
      <div className={`p-4 border border-dashed border-gray-300 rounded-lg ${className}`}>
        <div className="flex items-center justify-center text-gray-500">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>Content not found: {identifier}</span>
        </div>
      </div>
    );
  }

  // If children render prop is provided, use it
  if (children) {
    return <div className={className}>{children(content, content.contentType)}</div>;
  }

  // Otherwise, render based on content type
  return (
    <div className={className}>
      <ContentRenderer content={content} contentType={content.contentType} />
    </div>
  );
};

// Content Renderer based on content type
const ContentRenderer = ({ content, contentType }: { content: any; contentType: ContentType }) => {
  switch (contentType.name) {
    case "hero_section":
      return <HeroSectionRenderer content={content} />;
    case "content_section":
      return <ContentSectionRenderer content={content} />;
    case "text_block":
      return <TextBlockRenderer content={content} />;
    case "service_card":
      return <ServiceCardRenderer content={content} />;
    case "testimonial":
      return <TestimonialRenderer content={content} />;
    case "contact_info":
      return <ContactInfoRenderer content={content} />;
    case "cta_section":
      return <CTASectionRenderer content={content} />;
    case "feature_grid":
      return <FeatureGridRenderer content={content} />;
    case "partner_logo":
      return <PartnerLogoRenderer content={content} />;
    case "page_content":
      return <PageContentRenderer content={content} />;
    case "team_member":
      return <TeamMemberRenderer content={content} />;
    case "faq_item":
      return <FAQItemRenderer content={content} />;
    case "image_gallery":
      return <ImageGalleryRenderer content={content} />;
    case "about_section":
      return <AboutSectionRenderer content={content} />;
    case "services_banner":
      return <ServicesBannerRenderer content={content} />;
    case "key_features":
      return <KeyFeaturesRenderer content={content} />;
    case "trusted_partners":
      return <TrustedPartnersRenderer content={content} />;
    default:
      return <GenericContentRenderer content={content} contentType={contentType} />;
  }
};

// Hero Section Renderer
const HeroSectionRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <section className="relative bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-600 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-blue-800 rounded-full blur-3xl animate-pulse-slow animation-delay-1000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="animate-slide-up">
            {data.title ? (
              <EditableText
                identifier={content.identifier}
                fieldName="title"
                value={data.title}
                contentId={content._id}
                as="h1"
                className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6 animate-fade-in-up"
                placeholder="Enter hero title..."
              />
            ) : (
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6 animate-fade-in-up">
                <span className="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">Advanced</span>
                <span className="text-gray-900"> Technology &</span>
                <br />
                <span className="text-gray-900">Security Solutions</span>
              </h1>
            )}

            {data.subtitle ? (
              <EditableTextarea
                identifier={content.identifier}
                fieldName="subtitle"
                value={data.subtitle}
                contentId={content._id}
                as="p"
                className="text-xl text-gray-600 mb-8 leading-relaxed animate-fade-in-up animation-delay-300"
                placeholder="Enter hero subtitle..."
              />
            ) : (
              <p className="text-xl text-gray-600 mb-8 leading-relaxed animate-fade-in-up animation-delay-300">
                Empowering businesses across Equatorial Guinea with comprehensive IT services, cutting-edge web design, cybersecurity solutions, and technology infrastructure.
              </p>
            )}

            <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up animation-delay-600">
              {data.ctaText ? (
                <a
                  href={data.ctaUrl || '#'}
                  className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg rounded-lg font-semibold group transform hover:scale-105 transition-all duration-300"
                >
                  <EditableText
                    identifier={content.identifier}
                    fieldName="ctaText"
                    value={data.ctaText}
                    contentId={content._id}
                    placeholder="Enter CTA text..."
                  />
                  <svg className="ml-2 h-5 w-5 inline group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              ) : (
                <a
                  href="#"
                  className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg rounded-lg font-semibold group transform hover:scale-105 transition-all duration-300"
                >
                  Get Started Today
                  <svg className="ml-2 h-5 w-5 inline group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              )}

              {data.secondaryCtaText ? (
                <a
                  href={data.secondaryCtaUrl || '#'}
                  className="inline-block border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg rounded-lg border font-semibold group transform hover:scale-105 transition-all duration-300"
                >
                  <svg className="mr-2 h-5 w-5 inline group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2v-8a2 2 0 012-2z" />
                  </svg>
                  <EditableText
                    identifier={content.identifier}
                    fieldName="secondaryCtaText"
                    value={data.secondaryCtaText}
                    contentId={content._id}
                    placeholder="Enter secondary CTA text..."
                  />
                </a>
              ) : (
                <a
                  href="#"
                  className="inline-block border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg rounded-lg border font-semibold group transform hover:scale-105 transition-all duration-300"
                >
                  <svg className="mr-2 h-5 w-5 inline group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2v-8a2 2 0 012-2z" />
                  </svg>
                  View Our Work
                </a>
              )}
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative animate-slide-up animation-delay-600">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl transform hover:rotate-1 transition-transform duration-500">
              <img
                src={data.heroImage?.url || "https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"}
                alt={data.heroImageAlt || "Professional IT team working on technology solutions"}
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-blue-600/80 via-blue-600/20 to-transparent"></div>

              {/* Floating badges */}
              <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg animate-float">
                <div className="text-blue-600 font-semibold text-sm">
                  {data.badge1Text ? (
                    <EditableText
                      identifier={content.identifier}
                      fieldName="badge1Text"
                      value={data.badge1Text}
                      contentId={content._id}
                      placeholder="Badge 1 text..."
                    />
                  ) : "24/7 Support"}
                </div>
              </div>

              <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg animate-float animation-delay-600">
                <div className="text-blue-600 font-semibold text-sm">
                  {data.badge2Text ? (
                    <EditableText
                      identifier={content.identifier}
                      fieldName="badge2Text"
                      value={data.badge2Text}
                      contentId={content._id}
                      placeholder="Badge 2 text..."
                    />
                  ) : "Secure Solutions"}
                </div>
              </div>

              {/* Center experience badge */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="bg-white/95 backdrop-blur-sm rounded-full w-24 h-24 flex flex-col items-center justify-center shadow-lg animate-pulse-gentle">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {data.experienceYears ? (
                      <EditableText
                        identifier={content.identifier}
                        fieldName="experienceYears"
                        value={data.experienceYears}
                        contentId={content._id}
                        placeholder="Years..."
                      />
                    ) : "15+"}
                  </div>
                  <div className="text-xs text-gray-600 text-center leading-tight whitespace-pre-line">
                    {data.experienceLabel ? (
                      <EditableText
                        identifier={content.identifier}
                        fieldName="experienceLabel"
                        value={data.experienceLabel}
                        contentId={content._id}
                        placeholder="Experience label..."
                      />
                    ) : "Years\nExperience"}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Text Block Renderer
const TextBlockRenderer = ({ content }: { content: any }) => {
  const data = content.data;
  const alignmentClass = {
    left: "text-left",
    center: "text-center",
    right: "text-right",
  }[data.alignment] || "text-left";
  
  return (
    <div className={`prose max-w-none ${alignmentClass}`}>
      {data.title && (
        <h2 className="text-2xl font-bold mb-4">{data.title}</h2>
      )}
      {data.content && (
        <div dangerouslySetInnerHTML={{ __html: data.content }} />
      )}
    </div>
  );
};

// Service Card Renderer
const ServiceCardRenderer = ({ content }: { content: any }) => {
  const data = content.data;
  
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
      {data.image?.url && (
        <img 
          src={data.image.url} 
          alt={data.title}
          className="w-full h-48 object-cover rounded-lg mb-4"
        />
      )}
      
      <div className="flex items-center mb-4">
        {data.icon && (
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
            <span className="text-blue-600 text-xl">{data.icon}</span>
          </div>
        )}
        <EditableText
          identifier={content.identifier}
          fieldName="title"
          value={data.title}
          contentId={content._id}
          as="h3"
          className="text-xl font-bold text-gray-900"
          placeholder="Enter service title..."
        />
      </div>

      {data.description && (
        <EditableTextarea
          identifier={content.identifier}
          fieldName="description"
          value={data.description}
          contentId={content._id}
          as="div"
          className="text-gray-600 mb-4"
          placeholder="Enter service description..."
        />
      )}
      
      {data.features && Array.isArray(data.features) && data.features.length > 0 && (
        <ul className="space-y-2 mb-4">
          {data.features.map((feature: string, index: number) => (
            <li key={index} className="flex items-center text-sm text-gray-600">
              <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
              {feature}
            </li>
          ))}
        </ul>
      )}
      
      {data.ctaText && data.ctaUrl && (
        <a
          href={data.ctaUrl}
          className="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          {data.ctaText}
        </a>
      )}
    </div>
  );
};

// Testimonial Renderer
const TestimonialRenderer = ({ content }: { content: any }) => {
  const data = content.data;
  
  return (
    <div className="bg-gray-50 rounded-lg p-6">
      {data.content && (
        <div 
          className="text-gray-700 mb-4 italic"
          dangerouslySetInnerHTML={{ __html: data.content }}
        />
      )}
      
      <div className="flex items-center">
        {data.authorImage?.url && (
          <img 
            src={data.authorImage.url} 
            alt={data.authorName}
            className="w-12 h-12 rounded-full mr-4"
          />
        )}
        
        <div>
          <p className="font-semibold text-gray-900">{data.authorName}</p>
          {data.authorTitle && (
            <p className="text-sm text-gray-600">{data.authorTitle}</p>
          )}
          {data.authorCompany && (
            <p className="text-sm text-gray-600">{data.authorCompany}</p>
          )}
        </div>
        
        {data.rating && (
          <div className="ml-auto flex">
            {[...Array(5)].map((_, i) => (
              <span 
                key={i} 
                className={`text-lg ${i < data.rating ? 'text-yellow-400' : 'text-gray-300'}`}
              >
                ★
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Contact Info Renderer
const ContactInfoRenderer = ({ content }: { content: any }) => {
  const data = content.data;
  
  return (
    <div className="space-y-4">
      {data.title && (
        <h3 className="text-xl font-bold text-gray-900">{data.title}</h3>
      )}
      
      {data.address && (
        <div dangerouslySetInnerHTML={{ __html: data.address }} />
      )}
      
      {data.phone && (
        <p className="flex items-center text-gray-600">
          <span className="font-medium mr-2">Phone:</span>
          <a href={`tel:${data.phone}`} className="hover:text-blue-600">
            {data.phone}
          </a>
        </p>
      )}
      
      {data.email && (
        <p className="flex items-center text-gray-600">
          <span className="font-medium mr-2">Email:</span>
          <a href={`mailto:${data.email}`} className="hover:text-blue-600">
            {data.email}
          </a>
        </p>
      )}
      
      {data.hours && (
        <div>
          <p className="font-medium text-gray-900 mb-2">Business Hours:</p>
          <div dangerouslySetInnerHTML={{ __html: data.hours }} />
        </div>
      )}
      
      {data.socialLinks && Array.isArray(data.socialLinks) && data.socialLinks.length > 0 && (
        <div>
          <p className="font-medium text-gray-900 mb-2">Follow Us:</p>
          <div className="flex space-x-2">
            {data.socialLinks.map((link: any, index: number) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800"
              >
                {link.name}
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Image Gallery Renderer
const ImageGalleryRenderer = ({ content }: { content: any }) => {
  const data = content.data;
  const layout = data.layout || "grid";
  
  const layoutClasses = {
    grid: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
    masonry: "columns-1 md:columns-2 lg:columns-3 gap-4",
    carousel: "flex space-x-4 overflow-x-auto",
  };
  
  return (
    <div>
      {data.title && (
        <h3 className="text-xl font-bold text-gray-900 mb-6">{data.title}</h3>
      )}
      
      {data.images && Array.isArray(data.images) && (
        <div className={layoutClasses[layout as keyof typeof layoutClasses]}>
          {data.images.map((image: any, index: number) => (
            <div key={index} className="relative group">
              <img 
                src={image.url} 
                alt={image.alt || `Gallery image ${index + 1}`}
                className="w-full h-auto rounded-lg shadow-md hover:shadow-lg transition-shadow"
              />
              {image.caption && (
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                  <p className="text-sm">{image.caption}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Content Section Renderer
const ContentSectionRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <section className="py-16">
      <div className="container mx-auto px-6">
        {data.title && (
          <EditableText
            identifier={content.identifier}
            fieldName="title"
            value={data.title}
            contentId={content._id}
            as="h2"
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 text-center"
            placeholder="Enter section title..."
          />
        )}

        {data.subtitle && (
          <EditableText
            identifier={content.identifier}
            fieldName="subtitle"
            value={data.subtitle}
            contentId={content._id}
            as="p"
            className="text-xl text-gray-600 mb-8 text-center"
            placeholder="Enter section subtitle..."
          />
        )}

        {data.content && (
          <EditableTextarea
            identifier={content.identifier}
            fieldName="content"
            value={data.content}
            contentId={content._id}
            as="div"
            className="prose max-w-4xl mx-auto text-center"
            placeholder="Enter section content..."
          />
        )}

        {data.image?.url && (
          <div className="mt-8 text-center">
            <img
              src={data.image.url}
              alt={data.title || "Section image"}
              className="mx-auto rounded-lg shadow-lg max-w-full h-auto"
            />
          </div>
        )}
      </div>
    </section>
  );
};

// CTA Section Renderer
const CTASectionRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <section
      className="py-16 text-center"
      style={data.backgroundColor ? { backgroundColor: data.backgroundColor } : {}}
    >
      <div className="container mx-auto px-6">
        {data.title && (
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {data.title}
          </h2>
        )}

        {data.description && (
          <div
            className="text-xl mb-8 max-w-2xl mx-auto"
            dangerouslySetInnerHTML={{ __html: data.description }}
          />
        )}

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {data.primaryButtonText && data.primaryButtonUrl && (
            <a
              href={data.primaryButtonUrl}
              className="inline-block bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              {data.primaryButtonText}
            </a>
          )}

          {data.secondaryButtonText && data.secondaryButtonUrl && (
            <a
              href={data.secondaryButtonUrl}
              className="inline-block border border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
            >
              {data.secondaryButtonText}
            </a>
          )}
        </div>
      </div>
    </section>
  );
};

// Feature Grid Renderer
const FeatureGridRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <section className="py-16">
      <div className="container mx-auto px-6">
        {data.title && (
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 text-center">
            {data.title}
          </h2>
        )}

        {data.subtitle && (
          <p className="text-xl text-gray-600 mb-12 text-center">
            {data.subtitle}
          </p>
        )}

        {data.features && Array.isArray(data.features) && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {data.features.map((feature: any, index: number) => (
              <div key={index} className="text-center">
                {feature.icon && (
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-blue-600 text-2xl">{feature.icon}</span>
                  </div>
                )}

                {feature.title && (
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                )}

                {feature.description && (
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

// Partner Logo Renderer
const PartnerLogoRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <div className="text-center p-4">
      {data.logo?.url && (
        <div className="mb-4">
          {data.url ? (
            <a href={data.url} target="_blank" rel="noopener noreferrer">
              <img
                src={data.logo.url}
                alt={data.name}
                className="mx-auto h-16 w-auto grayscale hover:grayscale-0 transition-all"
              />
            </a>
          ) : (
            <img
              src={data.logo.url}
              alt={data.name}
              className="mx-auto h-16 w-auto"
            />
          )}
        </div>
      )}

      {data.name && (
        <h4 className="font-semibold text-gray-900">{data.name}</h4>
      )}

      {data.description && (
        <p className="text-sm text-gray-600 mt-2">{data.description}</p>
      )}
    </div>
  );
};

// Page Content Renderer
const PageContentRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <article className="prose max-w-4xl mx-auto">
      {data.featuredImage?.url && (
        <div className="mb-8">
          <img
            src={data.featuredImage.url}
            alt={data.title}
            className="w-full h-64 object-cover rounded-lg"
          />
        </div>
      )}

      {data.title && (
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          {data.title}
        </h1>
      )}

      {data.subtitle && (
        <p className="text-xl text-gray-600 mb-8">
          {data.subtitle}
        </p>
      )}

      {data.content && (
        <div dangerouslySetInnerHTML={{ __html: data.content }} />
      )}
    </article>
  );
};

// Team Member Renderer
const TeamMemberRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 text-center">
      {data.photo?.url && (
        <div className="mb-4">
          <img
            src={data.photo.url}
            alt={data.name}
            className="w-24 h-24 rounded-full mx-auto object-cover"
          />
        </div>
      )}

      {data.name && (
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          {data.name}
        </h3>
      )}

      {data.position && (
        <p className="text-blue-600 font-semibold mb-4">
          {data.position}
        </p>
      )}

      {data.bio && (
        <div
          className="text-gray-600 mb-4 text-sm"
          dangerouslySetInnerHTML={{ __html: data.bio }}
        />
      )}

      <div className="flex justify-center space-x-4">
        {data.email && (
          <a
            href={`mailto:${data.email}`}
            className="text-blue-600 hover:text-blue-800"
          >
            Email
          </a>
        )}

        {data.phone && (
          <a
            href={`tel:${data.phone}`}
            className="text-blue-600 hover:text-blue-800"
          >
            Phone
          </a>
        )}
      </div>

      {data.socialLinks && Array.isArray(data.socialLinks) && data.socialLinks.length > 0 && (
        <div className="mt-4 flex justify-center space-x-2">
          {data.socialLinks.map((link: any, index: number) => (
            <a
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800"
            >
              {link.name}
            </a>
          ))}
        </div>
      )}
    </div>
  );
};

// FAQ Item Renderer
const FAQItemRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <div className="border border-gray-200 rounded-lg p-6">
      {data.question && (
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          {data.question}
        </h3>
      )}

      {data.answer && (
        <div
          className="text-gray-600"
          dangerouslySetInnerHTML={{ __html: data.answer }}
        />
      )}

      {data.category && (
        <div className="mt-4">
          <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
            {data.category}
          </span>
        </div>
      )}
    </div>
  );
};

// Services Banner Renderer
const ServicesBannerRenderer = ({ content }: { content: any }) => {
  const data = content.data;
  const services = data.services || [
    "Cybersecurity Solutions", "Network Infrastructure", "Cloud Services",
    "IT Training", "Technical Support", "System Integration"
  ];

  return (
    <section className="py-16 bg-gradient-to-r from-primary to-primary/80 overflow-hidden">
      <div className="relative">
        <div className="flex animate-scroll-left">
          {[...services, ...services].map((service, index) => (
            <div key={index} className="flex-shrink-0 mx-8">
              <span className="text-white text-xl font-semibold whitespace-nowrap">
                {typeof service === 'string' ? service : service.name}
              </span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Key Features Renderer
const KeyFeaturesRenderer = ({ content }: { content: any }) => {
  const data = content.data;
  const features = data.features || [
    {
      icon: "Clock",
      title: "24/7 Support",
      description: "Round-the-clock monitoring and support for all your systems",
    },
    {
      icon: "Users",
      title: "Expert Team",
      description: "Certified professionals with years of industry experience",
    },
    {
      icon: "Award",
      title: "Quality Assured",
      description: "ISO certified processes and industry-leading standards",
    },
  ];

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case "Clock":
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case "Users":
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
        );
      case "Award":
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
          </svg>
        );
      default:
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          {data.title ? (
            <EditableText
              identifier={content.identifier}
              fieldName="title"
              value={data.title}
              contentId={content._id}
              as="h2"
              className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
              placeholder="Enter section title..."
            />
          ) : (
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose OfficeTech</h2>
          )}

          {data.subtitle ? (
            <EditableText
              identifier={content.identifier}
              fieldName="subtitle"
              value={data.subtitle}
              contentId={content._id}
              as="p"
              className="text-xl text-gray-600"
              placeholder="Enter section subtitle..."
            />
          ) : (
            <p className="text-xl text-gray-600">Excellence in every aspect of our service</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 text-primary">
                {getIcon(feature.icon)}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Trusted Partners Renderer
const TrustedPartnersRenderer = ({ content }: { content: any }) => {
  const data = content.data;
  const partners = data.partners || [
    { name: "Microsoft", logo: "https://via.placeholder.com/150x80/0078D4/FFFFFF?text=Microsoft" },
    { name: "Cisco", logo: "https://via.placeholder.com/150x80/1BA0D7/FFFFFF?text=Cisco" },
    { name: "VMware", logo: "https://via.placeholder.com/150x80/607078/FFFFFF?text=VMware" },
    { name: "Dell", logo: "https://via.placeholder.com/150x80/007DB8/FFFFFF?text=Dell" },
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          {data.title ? (
            <EditableText
              identifier={content.identifier}
              fieldName="title"
              value={data.title}
              contentId={content._id}
              as="h2"
              className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
              placeholder="Enter section title..."
            />
          ) : (
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Trusted Partners</h2>
          )}

          {data.description ? (
            <EditableText
              identifier={content.identifier}
              fieldName="description"
              value={data.description}
              contentId={content._id}
              as="p"
              className="text-xl text-gray-600"
              placeholder="Enter section description..."
            />
          ) : (
            <p className="text-xl text-gray-600">
              We work with industry-leading technology partners to deliver the best solutions for your business.
            </p>
          )}
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center">
          {partners.map((partner, index) => (
            <div key={index} className="flex items-center justify-center p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <img
                src={partner.logo}
                alt={`${partner.name} logo`}
                className="max-h-12 w-auto opacity-70 hover:opacity-100 transition-opacity duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// About Section Renderer (Who We Are)
const AboutSectionRenderer = ({ content }: { content: any }) => {
  const data = content.data;

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="animate-fade-in-up">
            {data.title ? (
              <EditableText
                identifier={content.identifier}
                fieldName="title"
                value={data.title}
                contentId={content._id}
                as="h2"
                className="text-3xl md:text-4xl font-bold text-gray-900 mb-6"
                placeholder="Enter section title..."
              />
            ) : (
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Who We Are</h2>
            )}

            {data.description ? (
              <EditableTextarea
                identifier={content.identifier}
                fieldName="description"
                value={data.description}
                contentId={content._id}
                as="p"
                className="text-xl text-gray-600 mb-8 leading-relaxed"
                placeholder="Enter section description..."
              />
            ) : (
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Leading technology and security solutions provider in Equatorial Guinea, delivering innovative IT services that empower businesses to thrive in the digital age.
              </p>
            )}

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              {/* Stats */}
              <div className="text-center p-4 rounded-lg border border-gray-100 hover:shadow-md transition-shadow duration-300">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {data.yearsExperience ? (
                    <EditableText
                      identifier={content.identifier}
                      fieldName="yearsExperience"
                      value={data.yearsExperience}
                      contentId={content._id}
                      placeholder="Years..."
                    />
                  ) : "15+"}
                </div>
                <div className="text-sm font-medium text-gray-700 mb-1">Years Experience</div>
                <div className="text-xs text-gray-500">Serving Equatorial Guinea</div>
              </div>

              <div className="text-center p-4 rounded-lg border border-gray-100 hover:shadow-md transition-shadow duration-300">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {data.clientsServed ? (
                    <EditableText
                      identifier={content.identifier}
                      fieldName="clientsServed"
                      value={data.clientsServed}
                      contentId={content._id}
                      placeholder="Clients..."
                    />
                  ) : "500+"}
                </div>
                <div className="text-sm font-medium text-gray-700 mb-1">Satisfied Clients</div>
                <div className="text-xs text-gray-500">Across multiple sectors</div>
              </div>

              <div className="text-center p-4 rounded-lg border border-gray-100 hover:shadow-md transition-shadow duration-300">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                  </svg>
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {data.satisfaction ? (
                    <EditableText
                      identifier={content.identifier}
                      fieldName="satisfaction"
                      value={data.satisfaction}
                      contentId={content._id}
                      placeholder="Satisfaction..."
                    />
                  ) : "99%"}
                </div>
                <div className="text-sm font-medium text-gray-700 mb-1">Client Satisfaction</div>
                <div className="text-xs text-gray-500">Quality guaranteed</div>
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="relative animate-slide-up animation-delay-300">
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <img
                src={data.image?.url || "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"}
                alt={data.imageAlt || "Professional IT team working"}
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent"></div>

              {/* Floating element */}
              <div className="absolute bottom-6 left-6 bg-white/95 backdrop-blur-sm rounded-lg px-4 py-3 animate-float">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-gray-900">
                      {data.badgeTitle ? (
                        <EditableText
                          identifier={content.identifier}
                          fieldName="badgeTitle"
                          value={data.badgeTitle}
                          contentId={content._id}
                          placeholder="Badge title..."
                        />
                      ) : "Certified Experts"}
                    </div>
                    <div className="text-xs text-gray-600">
                      {data.badgeSubtitle ? (
                        <EditableText
                          identifier={content.identifier}
                          fieldName="badgeSubtitle"
                          value={data.badgeSubtitle}
                          contentId={content._id}
                          placeholder="Badge subtitle..."
                        />
                      ) : "Industry Leaders"}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Generic Content Renderer (fallback)
const GenericContentRenderer = ({ content, contentType }: { content: any; contentType: ContentType }) => {
  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <h3 className="text-lg font-semibold mb-2">{contentType.label}</h3>
      <pre className="text-sm text-gray-600 overflow-auto">
        {JSON.stringify(content.data, null, 2)}
      </pre>
    </div>
  );
};

// Loading Skeleton
const ContentSkeleton = () => {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-3/4" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-5/6" />
      <Skeleton className="h-32 w-full" />
    </div>
  );
};
