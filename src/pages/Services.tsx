
import Layout from '@/components/Layout';
import { Shield, Wifi, GraduationCap, Eye, Server, Wrench, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/contexts/I18nContext';
import { DynamicContent } from '@/components/content/DynamicContent';
import { ContentProvider } from '@/components/content/ContentProvider';
import { ServicesGrid } from '@/components/content/ContentSection';

const ServicesContent = () => {
  const { t } = useTranslation();
  const services = [
    {
      icon: Shield,
      title: 'Cybersecurity Solutions',
      description: 'Comprehensive digital protection with advanced threat detection and response systems.',
      slug: 'cybersecurity'
    },
    {
      icon: Wifi,
      title: t('services.networkSolutions'),
      description: t('services.networkDesc'),
      slug: 'network-solutions'
    },
    {
      icon: Eye,
      title: 'Electronic Surveillance',
      description: 'Advanced CCTV and monitoring systems for comprehensive security coverage.',
      slug: 'surveillance'
    },
    {
      icon: GraduationCap,
      title: t('services.training'),
      description: t('services.trainingDesc'),
      slug: 'it-training'
    },
    {
      icon: Server,
      title: 'Data Protection',
      description: 'Secure backup solutions and data recovery services to protect your critical information.',
      slug: 'data-protection'
    },
    {
      icon: Wrench,
      title: 'System Integration',
      description: 'Seamless integration of technology systems for improved efficiency and workflow.',
      slug: 'system-integration'
    }
  ];

  const mainServices = [
    {
      title: t('services.networkSolutions'),
      description: t('services.networkDesc'),
      href: '/network-solutions',
      icon: Wifi,
      color: 'blue'
    },
    {
      title: t('services.domesticServices'),
      description: t('services.domesticDesc'),
      href: '/domestic-services',
      icon: Shield,
      color: 'green'
    },
    {
      title: t('services.training'),
      description: t('services.trainingDesc'),
      href: '/training-programs',
      icon: GraduationCap,
      color: 'purple'
    },
    {
      title: t('services.products'),
      description: t('services.productsDesc'),
      href: '/product-reselling',
      icon: Server,
      color: 'orange'
    }
  ];

  return (
    <>
      {/* Dynamic Services Hero Section */}
      <DynamicContent
        identifier="services-hero"
        className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20"
        fallback={
          <section className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                <span className="text-gradient">{t('services.title')}</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {t('services.subtitle')}
              </p>
            </div>
          </section>
        }
      />

      {/* Dynamic Services Grid */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Our Main Services
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Comprehensive technology solutions organized into key service areas
            </p>
          </div>

          <ServicesGrid
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          />
        </div>
      </section>

      {/* Additional Services Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <Card 
                  key={index} 
                  className="group hover:shadow-xl transition-all duration-300 border-0 bg-white hover:-translate-y-2"
                >
                  <CardContent className="p-8">
                    <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors duration-300">
                      <IconComponent className="w-8 h-8 text-blue-600 group-hover:text-white transition-colors duration-300" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {service.description}
                    </p>
                    <Button 
                      variant="ghost" 
                      className="text-blue-600 hover:text-blue-700 p-0 group-hover:translate-x-1 transition-transform duration-300"
                    >
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Contact us today to discuss your technology and security needs. 
            Our expert team is ready to help you find the perfect solution.
          </p>
          <Button 
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg"
          >
            Contact Us Today
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </section>
    </>
  );
};

const Services = () => {
  return (
    <ContentProvider defaultLanguage="en">
      <Layout>
        <ServicesContent />
      </Layout>
    </ContentProvider>
  );
};

export default Services;
