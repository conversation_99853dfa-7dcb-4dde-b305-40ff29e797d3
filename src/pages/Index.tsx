
import { HomePageLayout } from '@/components/SEOLayout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { HeroSection, ServicesGrid, TestimonialsSection, ContactSection } from '@/components/content/ContentSection';
import { DynamicContent } from '@/components/content/DynamicContent';
import { useContentContext } from '@/components/content/ContentProvider';
import { useLanguage } from '@/contexts/I18nContext';

// Legacy components (fallback)
import Hero from '@/components/Hero';
import ServicesBanner from '@/components/ServicesBanner';
import TrustedPartners from '@/components/TrustedPartners';
import WhoWeAre from '@/components/WhoWeAre';
import ServiceCards from '@/components/ServiceCards';
import KeyFeatures from '@/components/KeyFeatures';
import Testimonials from '@/components/Testimonials';
import CTASection from '@/components/CTASection';

const IndexContent = () => {
  const { language } = useContentContext();

  return (
    <>
      {/* Dynamic Hero Section */}
      <HeroSection
        identifier="home-hero"
        language={language}
        fallback={<Hero />}
      />

      {/* Dynamic Services Banner */}
      <DynamicContent
        identifier="services-banner"
        language={language}
        className="py-16"
        fallback={<ServicesBanner />}
      />

      {/* Trusted Partners */}
      <DynamicContent
        identifier="trusted-partners"
        language={language}
        className="py-16 bg-gray-50"
        fallback={<TrustedPartners />}
      />

      {/* Who We Are Section */}
      <DynamicContent
        identifier="who-we-are"
        language={language}
        className="py-16"
        fallback={<WhoWeAre />}
      />

      {/* Dynamic Services Grid */}
      <ServicesGrid
        language={language}
        className="py-16 bg-gray-50"
      />

      {/* Key Features */}
      <DynamicContent
        identifier="key-features"
        language={language}
        className="py-16"
        fallback={<KeyFeatures />}
      />

      {/* Dynamic Testimonials */}
      <TestimonialsSection
        language={language}
        className="py-16 bg-gray-50"
      />

      {/* CTA Section */}
      <DynamicContent
        identifier="cta-section"
        language={language}
        className="py-16"
        fallback={<CTASection />}
      />
    </>
  );
};

const Index = () => {
  const { language } = useLanguage();

  return (
    <ContentProvider defaultLanguage={language}>
      <HomePageLayout>
        <IndexContent />
      </HomePageLayout>
    </ContentProvider>
  );
};

export default Index;
